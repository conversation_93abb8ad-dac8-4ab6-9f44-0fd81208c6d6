import React, { useEffect, useState } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { Link } from "react-router-dom";
import { StarIcon } from "@/assets/svgs";
import SearchIcon from "@/assets/svgs/SearchIcon";

interface IListing {
  id: number;
  name: string;
  seller: string;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  created_at: string;
  updated_at: string;
  rating?: number;
  sponsored?: boolean;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const UserMarketplaceListPage = () => {
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 12,
    search: "",
    category: "",
    location: "",
    type: "",
    sort: "Recently Added",
  });
  const [searchInput, setSearchInput] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const { sdk } = useSDK();

  // Mock data for demonstration - replace with actual API call
  const mockListings = [
    {
      id: 1,
      name: "Premium Gaming Laptop",
      seller: "Tech Paradise",
      price: "1295.99",
      status: "active",
      description: "High-performance gaming laptop with RTX graphics",
      image: "/api/placeholder/300/200",
      category: "Electronics",
      created_at: "2024-01-15",
      updated_at: "2024-01-15",
      rating: 4.5,
      sponsored: true,
    },
    {
      id: 2,
      name: "Photography Services",
      seller: "PhotoMaster",
      price: "150.00",
      status: "active",
      description: "Professional photography for events and portraits",
      image: "/api/placeholder/300/200",
      category: "Services",
      created_at: "2024-01-14",
      updated_at: "2024-01-14",
      rating: 4.8,
      sponsored: true,
    },
    {
      id: 3,
      name: "Luxury Watch Collection",
      seller: "Luxury Finds",
      price: "3500.00",
      status: "active",
      description: "Collection of 3 premium watches, mint condition",
      image: "/api/placeholder/300/200",
      category: "Fashion",
      created_at: "2024-01-13",
      updated_at: "2024-01-13",
      rating: 4.2,
      sponsored: true,
    },
    {
      id: 4,
      name: "Premium Gaming Laptop",
      seller: "Tech Paradise",
      price: "1295.99",
      status: "active",
      description: "High-performance gaming laptop with RTX graphics",
      image: "/api/placeholder/300/200",
      category: "Electronics",
      created_at: "2024-01-12",
      updated_at: "2024-01-12",
      rating: 4.5,
      sponsored: false,
    },
    {
      id: 5,
      name: "Photography Services",
      seller: "PhotoMaster",
      price: "150.00",
      status: "active",
      description: "Professional photography for events and portraits",
      image: "/api/placeholder/300/200",
      category: "Services",
      created_at: "2024-01-11",
      updated_at: "2024-01-11",
      rating: 4.8,
      sponsored: false,
    },
    {
      id: 6,
      name: "Luxury Watch Collection",
      seller: "Luxury Finds",
      price: "3500.00",
      status: "active",
      description: "Collection of 3 premium watches, mint condition",
      image: "/api/placeholder/300/200",
      category: "Fashion",
      created_at: "2024-01-10",
      updated_at: "2024-01-10",
      rating: 4.2,
      sponsored: false,
    },
  ];

  const fetchListings = async () => {
    setLoading(true);
    try {
      // For now, using mock data - replace with actual API call
      // const result = await sdk.callRestAPI(
      //   {
      //     page: filters.page,
      //     limit: filters.limit,
      //     search: filters.search,
      //     status: "active"
      //   },
      //   "GET",
      //   "listing"
      // );

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      setListings(mockListings);
      setPagination({
        page: 1,
        limit: 12,
        total: 100,
        num_pages: 9,
        has_next: true,
        has_prev: false,
      });
    } catch (error) {
      console.error("Error fetching listings:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListings();
  }, [filters.page, filters.category, filters.search]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters((prev) => ({ ...prev, search: searchInput, page: 1 }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters((prev) => ({ ...prev, category, page: 1 }));
  };

  const handleLocationFilter = (location: string) => {
    setFilters((prev) => ({ ...prev, location, page: 1 }));
  };

  const handleTypeFilter = (type: string) => {
    setFilters((prev) => ({ ...prev, type, page: 1 }));
  };

  const handleSortChange = (sort: string) => {
    setFilters((prev) => ({ ...prev, sort, page: 1 }));
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <UserWrapper>
      <div className="h-full bg-[#0F2C59] overflow-auto">
        <div className="px-6 py-6">
          {/* Header */}
          <div className="mb-6">
            <h1 className="text-white text-3xl font-bold mb-2">Marketplace</h1>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-lg p-6 mb-6">
            {/* Top Row - Search Bar and Action Buttons */}
            <div className="flex gap-4 items-center mb-6">
              {/* Search Bar */}
              <div className="flex-1 relative">
                <form onSubmit={handleSearchSubmit} className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by item name, description, or category"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    value={searchInput}
                    onChange={handleSearchChange}
                  />
                </form>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 items-center">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                    />
                  </svg>
                  Filters
                </button>

                <button className="flex items-center gap-2 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 text-sm font-medium">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                    />
                  </svg>
                  Sort
                </button>

                <button className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 10h16M4 14h16M4 18h16"
                    />
                  </svg>
                </button>

                <button className="p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            {/* Filter Row */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => handleCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Categories</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Services">Services</option>
                  <option value="Fashion">Fashion</option>
                  <option value="Home">Home</option>
                </select>
              </div>

              {/* Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => handleTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All</option>
                  <option value="Product">Product</option>
                  <option value="Service">Service</option>
                </select>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <select
                  value={filters.location}
                  onChange={(e) => handleLocationFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="">All Locations</option>
                  <option value="New York">New York</option>
                  <option value="Los Angeles">Los Angeles</option>
                  <option value="Chicago">Chicago</option>
                </select>
              </div>

              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <select
                  value={filters.sort}
                  onChange={(e) => handleSortChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white"
                >
                  <option value="Recently Added">Recently Added</option>
                  <option value="Price: Low to High">Price: Low to High</option>
                  <option value="Price: High to Low">Price: High to Low</option>
                  <option value="Most Popular">Most Popular</option>
                </select>
              </div>
            </div>

            {/* Price Range */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range (eBa$)
              </label>
              <div className="flex gap-4 items-center">
                <input
                  type="text"
                  placeholder="Min"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <span className="text-gray-500">-</span>
                <input
                  type="text"
                  placeholder="Max"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
              </div>
            </div>

            {/* Checkboxes */}
            <div className="flex gap-6">
              <label className="flex items-center">
                <input type="checkbox" className="mr-2 rounded" />
                <span className="text-sm text-gray-700">
                  Sponsored listings only
                </span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2 rounded" />
                <span className="text-sm text-gray-700">
                  My location listings
                </span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2 rounded" />
                <span className="text-sm text-gray-700">My offers</span>
              </label>
              <label className="flex items-center">
                <input type="checkbox" className="mr-2 rounded" />
                <span className="text-sm text-gray-700">My favourites</span>
              </label>
            </div>
          </div>

          {loading ? (
            <MkdLoader />
          ) : (
            <>
              {/* Featured Listings */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-white text-xl font-semibold">
                    Featured Listings
                  </h2>
                  <span className="text-gray-300 text-sm">
                    Showing 1 of 15 featured listings
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {mockListings
                    .filter((listing) => listing.sponsored)
                    .map((listing) => (
                      <Link
                        key={listing.id}
                        to={`/user/marketplace/${listing.id}`}
                        className="bg-white rounded-lg overflow-hidden shadow-lg relative hover:shadow-xl transition-shadow"
                      >
                        {/* Sponsored Badge */}
                        <div className="absolute top-3 left-3 z-10">
                          <span className="bg-[#E63946] text-white px-2 py-1 rounded text-xs font-medium">
                            Sponsored
                          </span>
                        </div>

                        {/* Heart Icon */}
                        <div className="absolute top-3 right-3 z-10">
                          <button
                            className="bg-white bg-opacity-80 rounded-full p-2 hover:bg-opacity-100"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              // Handle favorite toggle
                            }}
                          >
                            <svg
                              className="w-5 h-5 text-gray-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                              />
                            </svg>
                          </button>
                        </div>

                        {/* Image */}
                        <div className="h-48 bg-gray-200 relative">
                          <img
                            src={listing.image}
                            alt={listing.name}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Content */}
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                            {listing.name}
                          </h3>
                          <p className="text-[#E63946] font-bold text-lg mb-2">
                            eb${listing.price}
                          </p>

                          <p className="text-gray-600 text-xs mb-3 line-clamp-2">
                            {listing.description}
                          </p>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              {renderStars(listing.rating)}
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                              <span className="text-xs text-gray-600">
                                {listing.seller}
                              </span>
                            </div>
                          </div>

                          <div className="mt-3 text-right">
                            <span className="text-xs text-gray-500">
                              Added Feb 15, 2024
                            </span>
                          </div>
                        </div>
                      </Link>
                    ))}
                </div>
              </div>

              {/* All Listings */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-white text-xl font-semibold">
                    All Listings
                  </h2>
                  <span className="text-gray-300 text-sm">
                    Showing 1 of 100 listings
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {mockListings.map((listing) => (
                    <Link
                      key={listing.id}
                      to={`/user/marketplace/${listing.id}`}
                      className="bg-white rounded-lg overflow-hidden shadow-lg relative hover:shadow-xl transition-shadow"
                    >
                      {/* Sponsored Badge */}
                      {listing.sponsored && (
                        <div className="absolute top-3 left-3 z-10">
                          <span className="bg-[#E63946] text-white px-2 py-1 rounded text-xs font-medium">
                            Sponsored
                          </span>
                        </div>
                      )}

                      {/* Heart Icon */}
                      <div className="absolute top-3 right-3 z-10">
                        <button
                          className="bg-white bg-opacity-80 rounded-full p-2 hover:bg-opacity-100"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            // Handle favorite toggle
                          }}
                        >
                          <svg
                            className="w-5 h-5 text-gray-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </div>

                      {/* Image */}
                      <div className="h-48 bg-gray-200 relative">
                        <img
                          src={listing.image}
                          alt={listing.name}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          {listing.name}
                        </h3>
                        <p className="text-[#E63946] font-bold text-lg mb-2">
                          eb${listing.price}
                        </p>

                        <p className="text-gray-600 text-xs mb-3 line-clamp-2">
                          {listing.description}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            {renderStars(listing.rating)}
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                            <span className="text-xs text-gray-600">
                              {listing.seller}
                            </span>
                          </div>
                        </div>

                        <div className="mt-3 text-right">
                          <span className="text-xs text-gray-500">
                            Added Feb 15, 2024
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>

              {/* Pagination */}
              {pagination && pagination.total > 0 && (
                <div className="flex justify-center items-center gap-2 mt-8">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="w-8 h-8 flex items-center justify-center rounded bg-white text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    ‹
                  </button>

                  {/* Page Numbers */}
                  {Array.from(
                    { length: Math.min(5, pagination.num_pages) },
                    (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`w-8 h-8 flex items-center justify-center rounded text-sm font-medium ${
                            currentPage === pageNum
                              ? "bg-[#E63946] text-white"
                              : "bg-white text-gray-600 hover:bg-gray-50"
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}

                  <button
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(prev + 1, pagination.num_pages)
                      )
                    }
                    disabled={currentPage === pagination.num_pages}
                    className="w-8 h-8 flex items-center justify-center rounded bg-white text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    ›
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserMarketplaceListPage;
