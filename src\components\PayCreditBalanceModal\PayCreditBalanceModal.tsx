import React from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "../InteractiveButton";

interface PayCreditBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PayCreditBalanceModal: React.FC<PayCreditBalanceModalProps> = ({
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modalDialog: "w-full max-w-lg",
      }}
    >
      <div className="p-6 bg-white rounded-lg shadow-lg w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-[#0F2C59]">
            Pay eBaCredit Balance
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            &times;
          </button>
        </div>

        <p className="text-gray-600 mb-6">
          Pay off your eBaCredit line balance within the grace period to avoid
          credit charges.
        </p>

        <div className="space-y-4 text-sm mb-6">
          <div className="flex justify-between">
            <span className="text-gray-500">Outstanding Balance:</span>
            <span className="font-bold text-gray-800">eBa$750.30</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Due In:</span>
            <span className="font-bold text-gray-800">15 days</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Grace Period Ends:</span>
            <span className="font-bold text-gray-800">May 15, 2025</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Penalty if Unpaid by Due Date</span>
            <span className="font-bold text-[#E63946]">
              Lose 30% of Credit Line
            </span>
          </div>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg text-center mb-6">
          <p className="text-gray-800">
            You have <span className="font-bold">eBa$2,580.45</span>
          </p>
        </div>

        <div className="mb-4">
          <label
            htmlFor="amount"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Enter amount to pay
          </label>
          <div className="relative">
            <input
              type="text"
              id="amount"
              defaultValue="750.30"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
              eBa$
            </span>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 text-blue-800 p-3 rounded-lg text-center text-sm mb-6 flex items-center">
          <span className="text-xl mr-2">ℹ️</span>
          <p>Paying in full grants you a 10% increase in credit limit.</p>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg text-center text-xs text-gray-600 mb-6">
          <p>
            You must pay your full balance within 30 days to maintain your
            credit line. Partial payments will not avoid penalties unless
            otherwise stated.
          </p>
        </div>

        <div className="flex justify-end space-x-4">
          <InteractiveButton
            onClick={onClose}
            className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            onClick={() => {
              /* Handle payment logic */
            }}
            className="px-6 py-2 bg-[#0F2C59] text-white rounded-lg hover:bg-blue-800"
          >
            Pay Now
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default PayCreditBalanceModal;
