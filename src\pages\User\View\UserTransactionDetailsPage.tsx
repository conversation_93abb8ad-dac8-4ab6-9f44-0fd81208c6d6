import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { UserWrapper } from "@/components/UserWrapper";
import { MkdLoader } from "@/components/MkdLoader";
import StarIcon from "@/assets/svgs/StarIcon";
import DownloadIcon from "@/assets/svgs/DownloadIcon";
import CheckIcon from "@/assets/svgs/CheckIcon";
import CalendarIcon from "@/assets/svgs/CalendarIcon";

interface ITransactionDetails {
  id: string;
  transactionId: string;
  date: string;
  time: string;
  type: string;
  status: string;
  itemName: string;
  itemDescription: string;
  amountPaid: string;
  amountPaidUSD: string;
  buyerName: string;
  buyerLocation: string;
  buyerMemberSince: string;
  buyerVerified: boolean;
  sellerName: string;
  sellerLocation: string;
  sellerType: string;
  sellerVerified: boolean;
  ebaAmount: string;
  usdFee: string;
  totalPaid: string;
  totalPaidUSD: string;
  deliveryStatus: string;
  deliveryDate: string;
  shippingMethod: string;
  shippingDate: string;
  estimatedDelivery: string;
  actualDelivery: string;
  trackingNumber: string;
  confirmationCode: string;
  agentName: string;
  agentPhone: string;
  vehicleNumber: string;
  deliveryAddress: string;
}

const SellerView = ({
  transactionDetails,
}: {
  transactionDetails: ITransactionDetails;
}) => (
  <>
    {/* Amount Earned */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Amount Earned
      </h2>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">Gross sale</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.ebaAmount}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Platform fee (10%)</span>
          <span className="font-semibold text-red-500">- eBa$ 34.95</span>
        </div>
        <hr />
        <div className="flex justify-between">
          <span className="font-semibold text-gray-800">Net received</span>
          <span className="font-bold text-gray-800">eBa$ 314.55</span>
        </div>
      </div>
    </div>

    {/* Delivery Tracking */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Delivery Tracking
      </h2>
      <div className="flex items-center mb-6">
        <div className="w-2.5 h-2.5 bg-blue-500 rounded-full mr-3"></div>
        <span className="font-semibold text-blue-600">In Transit</span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <p className="text-sm text-gray-500">Tracking Number</p>
          <p className="font-semibold text-gray-800 mb-4">
            {transactionDetails.trackingNumber.replace("29384756", "7654321")}
          </p>

          <p className="text-sm text-gray-500">Carrier</p>
          <p className="font-semibold text-gray-800 mb-4">eBa Delivery</p>

          <p className="text-sm text-gray-500">Shipping Date</p>
          <p className="font-semibold text-gray-800 mb-4">
            {transactionDetails.shippingDate}
          </p>

          <p className="text-sm text-gray-500">Estimated Delivery</p>
          <p className="font-semibold text-gray-800 mb-4">
            {transactionDetails.estimatedDelivery}
          </p>

          <p className="text-sm text-gray-500">Confirmation Code</p>
          <p className="font-semibold text-gray-800">CONF-1234</p>
        </div>
        <div>
          <p className="text-sm text-gray-500 mb-2">Delivery Agent</p>
          <div className="flex items-center mb-4">
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="Larry Brown"
              className="w-10 h-10 rounded-full mr-3"
            />
            <div>
              <p className="font-semibold text-gray-800">Larry Brown</p>
              <p className="text-sm text-gray-500">
                {transactionDetails.agentPhone}
              </p>
              <p className="text-sm text-gray-500">
                Vehicle: {transactionDetails.vehicleNumber}
              </p>
            </div>
          </div>
          <p className="text-sm text-gray-500 mb-2">Buyer Information</p>
          <div className="flex items-center">
            <img
              src="https://randomuser.me/api/portraits/men/44.jpg"
              alt="Alex Johnson"
              className="w-10 h-10 rounded-full mr-3"
            />
            <div>
              <p className="font-semibold text-gray-800">Alex Johnson</p>
              <p className="text-sm text-gray-500">
                {transactionDetails.buyerLocation}
              </p>
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {[...Array(4)].map((_, i) => (
                    <StarIcon key={i} className="w-4 h-4" />
                  ))}
                  <StarIcon className="w-4 h-4 text-gray-300" />
                </div>
                <span className="text-sm text-gray-500 ml-2">(28 ratings)</span>
              </div>
              <button className="text-sm text-blue-600 hover:underline">
                Contact Buyer
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-6 flex space-x-4">
        <button className="flex items-center px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700">
          <DownloadIcon className="w-4 h-4 mr-2" />
          Download Receipt
        </button>
        <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
          Refresh Status
        </button>
      </div>
    </div>

    {/* Transaction Timeline */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-6">
        Transaction Timeline
      </h2>
      <ul className="space-y-6">
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Shipping</h4>
            <p className="text-sm text-gray-500">Pending</p>
          </div>
        </li>
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Payment Received</h4>
            <p className="text-sm text-gray-500">Apr 18, 2025 • 14:32 PM</p>
            <p className="text-sm text-gray-500">
              Payment processed successfully
            </p>
          </div>
        </li>
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Order Placed</h4>
            <p className="text-sm text-gray-500">Apr 18, 2025 • 14:25 PM</p>
            <p className="text-sm text-gray-500">Buyer placed the order</p>
          </div>
        </li>
      </ul>
    </div>

    {/* Rate Buyer */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate Buyer</h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your delivery experience with Larry Brown?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "Kind",
          "Professional",
          "Polite & Respectful",
          "Friendly",
          "Prompt Payment",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the delivery service..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700">
          Submit Rating
        </button>
      </div>
    </div>

    {/* Rate Delivery Service */}
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Rate Delivery Service
      </h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your delivery experience with Larry Brown?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "On Time",
          "Professional",
          "Careful with Package",
          "Friendly",
          "Good Communication",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the delivery service..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700">
          Submit Rating
        </button>
      </div>
    </div>
  </>
);

const ServiceView = ({
  transactionDetails,
  isSellerCompleted,
  onMarkAsCompleted,
}: {
  transactionDetails: ITransactionDetails;
  isSellerCompleted: boolean;
  onMarkAsCompleted: () => void;
}) => (
  <>
    {/* Buyer and Seller Details */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Buyer Details
        </h3>
        <div className="flex items-center">
          <img
            src="https://randomuser.me/api/portraits/men/44.jpg"
            alt="Alex Johnson"
            className="w-10 h-10 rounded-full mr-4"
          />
          <div>
            <p className="font-semibold text-gray-800">
              {transactionDetails.buyerName}
            </p>
            <p className="text-sm text-gray-500">
              Credit Score: 750 - Location: {transactionDetails.buyerLocation}
            </p>
            <p className="text-sm text-gray-500">
              Member since {transactionDetails.buyerMemberSince}{" "}
              <span className="text-green-500">Verified</span>
            </p>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Seller Details
        </h3>
        <div className="flex items-center">
          <img
            src="https://randomuser.me/api/portraits/women/44.jpg"
            alt="Design Studio"
            className="w-10 h-10 rounded-full mr-4"
          />
          <div>
            <p className="font-semibold text-gray-800">Design Studio</p>
            <p className="text-sm text-gray-500">
              4.5 - Location: {transactionDetails.sellerLocation}
            </p>
            <p className="text-sm text-gray-500">
              Service Provider <span className="text-green-500">Verified</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Total Paid */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Paid</h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">eBa Amount</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.ebaAmount}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">USD Fee (1%)</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.usdFee}
          </span>
        </div>
        <hr />
        <div className="flex justify-between items-center">
          <span className="font-semibold text-gray-800">Total Paid</span>
          <div className="text-right">
            <p className="font-bold text-lg text-gray-800">
              {transactionDetails.totalPaid}
            </p>
            <p className="text-sm text-gray-500">
              ≈ {transactionDetails.totalPaidUSD}
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Service Completion & Confirmation */}
    {isSellerCompleted ? (
      <div className="bg-white rounded-lg p-6 mb-6">
        <div className="bg-yellow-100 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Seller marked this service as completed on Apr 25, 2025 at 4:15
                PM.
                <br />
                Please confirm if you have received the service.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-6">
          <div>
            <p className="text-sm text-gray-500">Appointment Date</p>
            <div className="flex items-center mt-1">
              <CalendarIcon className="w-5 h-5 text-gray-500 mr-2" />
              <p className="font-semibold text-gray-800">Apr 25, 2025</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Duration</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="font-semibold text-gray-800">2 hours</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Time Slot</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="font-semibold text-gray-800">2:00 PM - 4:00 PM</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Payment Method</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              <p className="font-semibold text-gray-800">Escrow</p>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={onMarkAsCompleted}
            className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg flex items-center"
          >
            <CheckIcon className="w-5 h-5 mr-2" />
            Mark Service as Completed
          </button>
        </div>
      </div>
    ) : (
      <div className="bg-white rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Service Completion & Confirmation
          </h3>
          <button className="text-sm text-blue-600 hover:underline">
            Refresh Status
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <p className="text-sm text-gray-500 mb-2">Service Details</p>
            <div className="space-y-2">
              <p className="flex items-center">
                <span className="text-green-500 mr-2">✔</span>
                <span className="font-semibold text-gray-800">Completed</span>
                <span className="text-sm text-gray-500 ml-2">
                  Apr 25, 2023 at 16:20 PM
                </span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">
                  Appointment Date:
                </span>
                <span className="text-sm text-gray-600 ml-2">Apr 25, 2023</span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">Time Slot:</span>
                <span className="text-sm text-gray-600 ml-2">
                  2:00 PM - 4:00 PM
                </span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">Duration:</span>
                <span className="text-sm text-gray-600 ml-2">2 hours</span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">
                  Payment Method:
                </span>
                <span className="text-sm text-gray-600 ml-2">Escrow</span>
              </p>
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm font-semibold text-gray-800 mb-2">
              Service Status
            </p>
            <div className="flex items-start">
              <span className="text-green-500 mr-2">✔</span>
              <p className="text-sm text-gray-600">
                Service marked complete by both parties on Apr 25, 2023 - 16:20.
              </p>
            </div>
            <div className="flex items-start mt-2">
              <span className="text-green-500 mr-2">✔</span>
              <p className="text-sm text-gray-600">
                Payment has been released to the seller.
              </p>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Request Refund */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        Request Refund
      </h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Reason
          </label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option>Select a reason</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows={3}
            placeholder="Describe your issue with the service received"
          ></textarea>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Supporting Evidence
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <p>Drag files here or click to upload</p>
            <p className="text-xs text-gray-500">Max file size: 10MB</p>
          </div>
        </div>
        <div className="flex justify-end">
          <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
            Request Refund
          </button>
        </div>
      </div>
    </div>

    {/* Transaction Timeline */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-6">
        Transaction Timeline
      </h2>
      <ul className="space-y-6">
        {/* Buyer Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Buyer Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:20 PM</p>
            <p className="text-sm text-gray-500">
              Service confirmed as completed by buyer
            </p>
          </div>
        </li>
        {/* Seller Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Seller Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:05 PM</p>
            <p className="text-sm text-gray-500">
              Service provider marked the service as completed
            </p>
          </div>
        </li>
        {/* Service Booked */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Service Booked (Confirmed)
            </h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:34 PM</p>
            <p className="text-sm text-gray-500">
              Service appointment confirmed and scheduled
            </p>
          </div>
        </li>
        {/* Payment Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Payment Completed</h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:32 PM</p>
            <p className="text-sm text-gray-500">
              Payment processed successfully
            </p>
          </div>
        </li>
      </ul>
    </div>

    {/* Rate Seller */}
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate Seller</h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your overall experience with the service?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "Professional",
          "On Time",
          "Friendly",
          "Met Expectations",
          "Would Book Again",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the service provider..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
          Submit Rating
        </button>
      </div>
    </div>
  </>
);

const ServiceProviderView = ({
  transactionDetails,
}: {
  transactionDetails: ITransactionDetails;
}) => (
  <>
    {/* Amount Earned */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Amount Earned
      </h2>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">Gross sale</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.ebaAmount}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Platform fee (10%)</span>
          <span className="font-semibold text-red-500">- eBa$ 34.95</span>
        </div>
        <hr />
        <div className="flex justify-between">
          <span className="font-semibold text-gray-800">Net received</span>
          <span className="font-bold text-gray-800">eBa$ 314.55</span>
        </div>
      </div>
    </div>

    {/* Service Completion & Confirmation */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          Service Completion & Confirmation
        </h3>
        <button className="text-sm text-red-500 hover:underline flex items-center">
          Refresh Status
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <p className="text-sm text-gray-500 mb-2">Service Details</p>
          <div className="space-y-2">
            <p className="flex items-center font-semibold text-gray-800">
              <span className="text-green-500 mr-2">✔</span>
              Completed Apr 25, 2023 at 16:20 PM
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">
                Appointment Date:
              </span>
              <span className="text-sm text-gray-600 ml-2">Apr 25, 2023</span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">Time Slot:</span>
              <span className="text-sm text-gray-600 ml-2">
                2:00 PM - 4:00 PM
              </span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">Duration:</span>
              <span className="text-sm text-gray-600 ml-2">2 hours</span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">
                Payment Method:
              </span>
              <span className="text-sm text-gray-600 ml-2">Escrow</span>
            </p>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <p className="text-sm font-semibold text-gray-800 mb-2">
            Service Status
          </p>
          <div className="flex items-start">
            <span className="text-green-500 mr-2">✔</span>
            <p className="text-sm text-gray-600">
              Service marked completed by both parties on Apr 25, 2023 - 16:20.
            </p>
          </div>
          <div className="flex items-start mt-2">
            <span className="text-green-500 mr-2">✔</span>
            <p className="text-sm text-gray-600">
              Payment has been released to the seller.
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Transaction Timeline */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-6">
        Transaction Timeline
      </h2>
      <ul className="space-y-6">
        {/* Buyer Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <CheckIcon className="w-4 h-4 text-white" />
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Buyer Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:20 PM</p>
            <p className="text-sm text-gray-500">
              Service confirmed as completed by buyer
            </p>
          </div>
        </li>
        {/* Seller Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Seller Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:05 PM</p>
            <p className="text-sm text-gray-500">
              Service provider marked the service as completed
            </p>
          </div>
        </li>
        {/* Service Booked */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Service Booked (Confirmed)
            </h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:34 PM</p>
            <p className="text-sm text-gray-500">
              Service appointment confirmed and scheduled
            </p>
          </div>
        </li>
        {/* Payment Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Payment Completed</h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:32 PM</p>
            <p className="text-sm text-gray-500">
              Payment processed successfully
            </p>
          </div>
        </li>
      </ul>
    </div>

    {/* Rate Buyer */}
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate Buyer</h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your delivery experience with Larry Brown?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "Kind",
          "Professional",
          "Polite & Respectful",
          "Friendly",
          "Prompt Payment",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the delivery service..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
          Submit Rating
        </button>
      </div>
    </div>
  </>
);

const UserTransactionDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [transactionDetails, setTransactionDetails] =
    useState<ITransactionDetails | null>(null);
  const [view, setView] = useState("buyer"); // 'buyer', 'seller', 'service', 'service_provider'
  const [isSellerCompleted, setIsSellerCompleted] = useState(true);

  useEffect(() => {
    let mockData;
    if (view === "service_provider") {
      mockData = {
        id: id || "4",
        transactionId: "TRX-29384756",
        date: "Apr 18, 2023",
        time: "14:32 UTC",
        type: "Sale",
        status: "Processing",
        itemName: "Digital Camera",
        itemDescription: "High-resolution digital camera with accessories",
        amountPaid: "eBa$ 349.50",
        amountPaidUSD: "$506.78 USD",
        buyerName: "Alex Johnson",
        buyerLocation: "New York, USA",
        buyerMemberSince: "Jan 2023",
        buyerVerified: true,
        sellerName: "Design Studio",
        sellerLocation: "California, USA",
        sellerType: "Service Provider",
        sellerVerified: true,
        ebaAmount: "eBa$ 349.50",
        usdFee: "$3.49",
        totalPaid: "eBa$ 349.50",
        totalPaidUSD: "$349.50 USD",
        deliveryStatus: "Completed",
        deliveryDate: "Apr 25, 2023 at 16:20 PM",
        shippingMethod: "N/A",
        shippingDate: "N/A",
        estimatedDelivery: "N/A",
        actualDelivery: "N/A",
        trackingNumber: "N/A",
        confirmationCode: "N/A",
        agentName: "N/A",
        agentPhone: "N/A",
        vehicleNumber: "N/A",
        deliveryAddress: "N/A",
      };
    } else if (view === "service") {
      mockData = {
        id: id || "3",
        transactionId: "TRX-29384756",
        date: "Apr 18, 2023",
        time: "14:32 UTC",
        type: "Purchase",
        status: isSellerCompleted ? "Pending Confirmation" : "Completed",
        itemName: "Website Design Consultation",
        itemDescription:
          "Professional website design consultation and wireframing",
        amountPaid: "eBa$ 349.50",
        amountPaidUSD: "$506.78 USD",
        buyerName: "Alex Johnson",
        buyerLocation: "New York, USA",
        buyerMemberSince: "Jan 2023",
        buyerVerified: true,
        sellerName: "Design Studio",
        sellerLocation: "California, USA",
        sellerType: "Service Provider",
        sellerVerified: true,
        ebaAmount: "eBa$ 349.50",
        usdFee: "$3.49",
        totalPaid: "eBa$ 349.50",
        totalPaidUSD: "$349.50 USD",
        deliveryStatus: "Completed",
        deliveryDate: "Apr 25, 2023 at 16:20 PM",
        shippingMethod: "N/A",
        shippingDate: "N/A",
        estimatedDelivery: "N/A",
        actualDelivery: "N/A",
        trackingNumber: "N/A",
        confirmationCode: "N/A",
        agentName: "N/A",
        agentPhone: "N/A",
        vehicleNumber: "N/A",
        deliveryAddress: "N/A",
      };
    } else {
      mockData = {
        id: id || "2",
        transactionId: "TRX-29384756",
        date: "Apr 19, 2023",
        time: "14:32 UTC",
        type: "Purchase",
        status: "Completed",
        itemName: "Digital Camera",
        itemDescription: "High-resolution digital camera with accessories",
        amountPaid: "eBa$ 349.50",
        amountPaidUSD: "$349.76 USD",
        buyerName: "Alex Johnson",
        buyerLocation: "New York, USA",
        buyerMemberSince: "Jan 2023",
        buyerVerified: true,
        sellerName: "Camera World",
        sellerLocation: "California, USA",
        sellerType: "Business Seller",
        sellerVerified: true,
        ebaAmount: "eBa$ 349.50",
        usdFee: "$3.49",
        totalPaid: "eBa$ 349.50",
        totalPaidUSD: "$349.50 USD",
        deliveryStatus: "Delivered",
        deliveryDate: "Apr 22, 2023 at 11:23 AM",
        shippingMethod: "eBa Delivery",
        shippingDate: "Apr 19, 2023",
        estimatedDelivery: "Apr 22, 2023",
        actualDelivery: "Apr 22, 2023",
        trackingNumber: "eBa-29384756-TRK",
        confirmationCode: "CONF-12345",
        agentName: "Larry Brown",
        agentPhone: "+****************",
        vehicleNumber: "XYZ-9876",
        deliveryAddress:
          "1234 Main Street Apt 5B\nNew York, NY 10001 United States",
      };
    }
    // Mock data based on the uploaded image - specifically for transaction ID "2" (Digital Camera)
    const mockDetails: ITransactionDetails = mockData;

    // Simulate loading
    setTimeout(() => {
      setTransactionDetails(mockDetails);
      setLoading(false);
    }, 500);
  }, [id, view, isSellerCompleted]);

  const renderView = () => {
    switch (view) {
      case "seller":
        return <SellerView transactionDetails={transactionDetails!} />;
      case "service_provider":
        return <ServiceProviderView transactionDetails={transactionDetails!} />;
      case "service":
        return (
          <ServiceView
            transactionDetails={transactionDetails!}
            isSellerCompleted={isSellerCompleted}
            onMarkAsCompleted={() => setIsSellerCompleted(false)}
          />
        );
      case "buyer":
      default:
        return (
          transactionDetails && (
            <>
              {/* Buyer and Seller Details */}
              <div className="grid grid-cols-2 gap-6 mb-6">
                {/* Buyer Details */}
                <div className="bg-white rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Buyer Details
                  </h3>
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <svg
                        className="w-5 h-5 text-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {transactionDetails.buyerName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {transactionDetails.buyerLocation}
                      </p>
                      <p className="text-sm text-gray-600">
                        Member since {transactionDetails.buyerMemberSince}
                        {transactionDetails.buyerVerified && (
                          <span className="text-green-600 ml-2">Verified</span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Seller Details */}
                <div className="bg-white rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Seller Details
                  </h3>
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                      <svg
                        className="w-5 h-5 text-orange-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">
                        {transactionDetails.sellerName}
                      </p>
                      <p className="text-sm text-gray-600">
                        {transactionDetails.sellerLocation}
                      </p>
                      <p className="text-sm text-gray-600">
                        {transactionDetails.sellerType}
                        {transactionDetails.sellerVerified && (
                          <span className="text-green-600 ml-2">Verified</span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Total Paid */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Total Paid
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">eBa Amount</span>
                    <span className="font-semibold text-gray-900">
                      {transactionDetails.ebaAmount}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">USD Fee (1%)</span>
                    <span className="font-semibold text-gray-900">
                      {transactionDetails.usdFee}
                    </span>
                  </div>
                  <hr className="my-3" />
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-900">
                      Total Paid
                    </span>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">
                        {transactionDetails.totalPaid}
                      </p>
                      <p className="text-sm text-gray-500">
                        = {transactionDetails.totalPaidUSD}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Delivery Tracking */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Delivery Tracking
                  </h3>
                  <button className="text-sm text-[#E63946] hover:text-red-800 flex items-center">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Refresh Status
                  </button>
                </div>

                <div className="flex items-center mb-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <span className="font-semibold text-green-700">
                    Delivered
                  </span>
                  <span className="text-gray-600 ml-2">
                    {transactionDetails.deliveryDate}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-8">
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Shipping Method
                      </p>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <span className="font-semibold text-gray-900">
                          {transactionDetails.shippingMethod}
                        </span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Shipping Date
                      </p>
                      <span className="font-semibold text-gray-900">
                        {transactionDetails.shippingDate}
                      </span>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Tracking Number
                      </p>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <span className="font-semibold text-gray-900">
                          {transactionDetails.trackingNumber}
                        </span>
                        <button className="ml-2 text-[#E63946] hover:text-red-800">
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Confirmation Code
                      </p>
                      <span className="font-semibold text-gray-900">
                        {transactionDetails.confirmationCode}
                      </span>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">Agent Name</p>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        <span className="font-semibold text-gray-900">
                          {transactionDetails.agentName}
                        </span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">Agent Phone</p>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                        <span className="font-semibold text-gray-900">
                          {transactionDetails.agentPhone}
                        </span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Vehicle Number
                      </p>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-gray-500 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <span className="font-semibold text-gray-900">
                          {transactionDetails.vehicleNumber}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">
                        Estimated Delivery
                      </p>
                      <span className="font-semibold text-gray-900">
                        {transactionDetails.estimatedDelivery}
                      </span>
                    </div>

                    <div className="mt-4">
                      <p className="text-sm text-gray-500 mb-1">
                        Actual Delivery
                      </p>
                      <span className="font-semibold text-gray-900">
                        {transactionDetails.actualDelivery}
                      </span>
                    </div>

                    <div className="mt-4">
                      <button className="flex items-center px-4 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        Download PDF
                      </button>
                    </div>

                    <div className="mt-6">
                      <p className="text-sm text-gray-500 mb-2">
                        Delivery Address
                      </p>
                      <div className="bg-gray-50 rounded-lg p-3">
                        <p className="text-sm font-semibold text-gray-900 whitespace-pre-line">
                          {transactionDetails.deliveryAddress}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Request Refund Section */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Request Refund
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reason
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent">
                      <option>Select a reason</option>
                      <option>Item not as described</option>
                      <option>Item damaged</option>
                      <option>Item not received</option>
                      <option>Wrong item received</option>
                      <option>Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                      rows={4}
                      placeholder="Enter details here"
                    ></textarea>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Supporting Evidence
                    </label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <svg
                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <p className="text-sm text-gray-500">
                        Drag files here or click to upload
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        PNG, JPG, PDF up to 10MB
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                      Request Refund
                    </button>
                  </div>
                </div>
              </div>

              {/* Transaction Timeline */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                  Transaction Timeline
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-green-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900">
                          Item Delivered
                        </h4>
                        <span className="text-sm text-gray-500">
                          Apr 22, 2023 • 11:23 AM
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Package was delivered and signed for by recipient
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900">
                          Shipping in Progress
                        </h4>
                        <span className="text-sm text-gray-500">
                          Apr 19, 2023 • 08:45 AM
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Package picked up by carrier
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900">
                          Item Shipped
                        </h4>
                        <span className="text-sm text-gray-500">
                          Apr 19, 2023 • 08:32 AM
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Seller has shipped the item
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-red-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900">
                          Payment Completed
                        </h4>
                        <span className="text-sm text-gray-500">
                          Apr 18, 2023 • 14:32 PM
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Payment processed successfully
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Rate Seller */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Rate Seller
                </h3>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-700 mb-3">
                      How was your delivery experience with Larry Brown?
                    </p>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          className="text-gray-300 hover:text-yellow-400 text-2xl"
                        >
                          ⭐
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-3">
                      Quick Tags
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {[
                        "On Time",
                        "Professional",
                        "Friendly Seller",
                        "Fast",
                        "Item as Described",
                      ].map((tag) => (
                        <button
                          key={tag}
                          className="px-3 py-1 text-xs border border-gray-300 rounded-full text-gray-700 hover:bg-gray-50"
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Comments
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                      rows={3}
                      placeholder="Share your experience with the delivery service..."
                    ></textarea>
                  </div>

                  <div className="flex justify-end">
                    <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                      Submit Rating
                    </button>
                  </div>
                </div>
              </div>

              {/* Rate Delivery Service */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Rate Delivery Service
                </h3>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-700 mb-3">
                      How was your delivery experience with Larry Brown?
                    </p>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          className="text-gray-300 hover:text-yellow-400 text-2xl"
                        >
                          ⭐
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-3">
                      Quick Tags
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {[
                        "On Time",
                        "Professional",
                        "Careful with Package",
                        "Friendly",
                        "Good Communication",
                      ].map((tag) => (
                        <button
                          key={tag}
                          className="px-3 py-1 text-xs border border-gray-300 rounded-full text-gray-700 hover:bg-gray-50"
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Comments
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                      rows={3}
                      placeholder="Share your experience with the delivery service..."
                    ></textarea>
                  </div>

                  <div className="flex justify-end">
                    <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                      Submit Rating
                    </button>
                  </div>
                </div>
              </div>
            </>
          )
        );
    }
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  if (!transactionDetails) {
    return (
      <UserWrapper>
        <div className="p-6 bg-[#0F2C59] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Transaction not found</div>
            <button
              onClick={() => navigate("/user/transactions")}
              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Back to Transactions
            </button>
          </div>
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="p-6 bg-[#0D3166] min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => navigate("/user/transactions")}
            className="text-white hover:text-gray-300 flex items-center text-lg"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Transaction Details
          </button>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setView("buyer")}
              className={`px-3 py-1 rounded-md text-sm ${view === "buyer" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Buyer View
            </button>
            <button
              onClick={() => setView("seller")}
              className={`px-3 py-1 rounded-md text-sm ${view === "seller" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Seller View
            </button>
            <button
              onClick={() => setView("service")}
              className={`px-3 py-1 rounded-md text-sm ${view === "service" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Service Recipient View
            </button>
            <button
              onClick={() => setView("service_provider")}
              className={`px-3 py-1 rounded-md text-sm ${view === "service_provider" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Service Provider View
            </button>
          </div>
        </div>

        {transactionDetails && (
          <>
            {/* Transaction Summary */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Transaction Summary
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div>
                  <p className="text-sm text-gray-500">Transaction ID</p>
                  <p className="font-semibold text-gray-800">
                    #{transactionDetails.transactionId}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Date & Time</p>
                  <p className="font-semibold text-gray-800">
                    {transactionDetails.date}
                  </p>
                  <p className="text-sm text-gray-500">
                    {transactionDetails.time}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <p className="font-semibold text-gray-800">
                    {transactionDetails.type}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      transactionDetails.status === "Completed"
                        ? "bg-green-100 text-green-800"
                        : transactionDetails.status === "Pending Confirmation"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {transactionDetails.status}
                  </span>
                </div>
              </div>

              {/* Item Details */}
              <div className="flex items-center bg-gray-50 rounded-lg p-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                  <svg
                    className="w-8 h-8 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800">
                    {transactionDetails.itemName}
                  </h3>
                  {view === "service_provider" && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2">
                      Service
                    </span>
                  )}
                  <p className="text-sm text-gray-600">
                    {transactionDetails.itemDescription}
                  </p>
                  <button className="text-sm text-red-500 hover:underline mt-1">
                    View service details
                  </button>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Amount Paid</p>
                  <p className="text-xl font-bold text-gray-800">
                    {transactionDetails.amountPaid}
                  </p>
                  <p className="text-sm text-gray-500">
                    ≈ {transactionDetails.amountPaidUSD}
                  </p>
                </div>
              </div>
            </div>
            {renderView()}
          </>
        )}
      </div>
    </UserWrapper>
  );
};

export default UserTransactionDetailsPage;

// Also export as UserViewTransactionPage for backward compatibility
export { UserTransactionDetailsPage as UserViewTransactionPage };
