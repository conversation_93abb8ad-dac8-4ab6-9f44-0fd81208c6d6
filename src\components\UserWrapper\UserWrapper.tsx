import React, { Suspense, memo } from "react";
import { TopHeader } from "@/components/TopHeader";
import { Spinner } from "@/assets/svgs";
import { LazyLoad } from "@/components/LazyLoad";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import UserHeader from "../UserHeader";

interface UserWrapperProps {
  children: React.ReactNode;
}

const UserWrapper = ({ children }: UserWrapperProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <>
      <LazyLoad>
        <div
          className={`relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden`}
        >
          <UserHeader />
          <div
            className={`grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden`}
          >
            {/* <TopHeader /> */}
            <Suspense
              fallback={
                <div
                  className={`flex h-full max-h-full min-h-full w-full items-center justify-center`}
                >
                  <Spinner size={40} color={THEME_COLORS[mode].PRIMARY} />
                </div>
              }
            >
              <div className="h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden bg-[#F0F4F8]">
                {children}
              </div>
            </Suspense>
          </div>
        </div>
      </LazyLoad>
    </>
  );
};

export default memo(UserWrapper);
