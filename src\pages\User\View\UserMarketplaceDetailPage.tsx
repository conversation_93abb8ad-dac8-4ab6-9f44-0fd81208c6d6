import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { UserWrapper } from "../../../components/UserWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { StarIcon } from "@/assets/svgs";

interface ISellerInfo {
  name: string;
  rating: number;
  reviews: number;
  member_since: string;
  credit_score: string;
  location: string;
  verified: boolean;
}

interface IBookingOptions {
  service_location: string;
  availability: string;
  time_slots: string;
  payment: string;
}

interface IListing {
  id: number;
  name: string;
  seller: string;
  seller_id?: number;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  created_at: string;
  updated_at: string;
  rating?: number;
  sponsored?: boolean;
  type?: string;
  tags?: string[];
  seller_info?: ISellerInfo;
  availableSizes?: { short: string; long: string }[];
  price_per_hour?: boolean;
  booking_options?: IBookingOptions;
}

const UserMarketplaceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [listing, setListing] = useState<IListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showMakeOfferModal, setShowMakeOfferModal] = useState(false);
  const [offerAmount, setOfferAmount] = useState("");
  const [offerMessage, setOfferMessage] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState("L");
  const { sdk } = useSDK();

  // Mock data - same as in UserMarketplaceListPage
  const mockListings = [
    {
      id: 1,
      name: "Premium Hoodie",
      seller: "TechTreasures",
      price: "1,299.99",
      status: "active",
      description: `This premium hoodie is perfect for any occasion. Made from high-quality materials, it's comfortable and stylish.

Key specifications:
- 100% premium cotton
- Fleece-lined interior
- Front kangaroo pocket
- Ribbed cuffs and hem

This hoodie is brand new with tags.

This is perfect for a casual look or for a comfortable day at home.`,
      image:
        "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=2000",
      category: "Clothing > Hoodies & Sweatshirts",
      created_at: "2024-04-20", // Assuming "Listed 3 days ago" from a certain date
      updated_at: "2024-04-20",
      rating: 4.2,
      sponsored: true,
      type: "Item",
      tags: ["Sponsored", "New"],
      availableSizes: [
        { short: "S", long: "Small" },
        { short: "M", long: "Medium" },
        { short: "L", long: "Large" },
        { short: "XL", long: "X-Large" },
        { short: "2XL", long: "XX-Large" },
      ],
      seller_info: {
        name: "TechTreasures",
        rating: 4.2,
        reviews: 98,
        member_since: "January 2024",
        credit_score: "87/100",
        location: "Toronto, ON",
        verified: true,
      },
    },
    {
      id: 2,
      name: "Professional Photography Services - Corporate Headshots",
      seller: "TechTreasures",
      price: "150.00",
      status: "active",
      description: `This listing is for professional photography sessions specializing in corporate headshots and portrait photography. Perfect for business professionals, LinkedIn profiles, and corporate branding needs.

What's included:
- 1-hour professional photo session
- Professional lighting setup
- Multiple outfit changes (up to 3)
- 20+ high-resolution edited photos
- Online gallery for easy download
- Commercial usage rights included
- Same-day turnaround available

Equipment used: Canon EOS R5, professional studio lighting, various lenses for different looks. Session can be conducted at my studio or your preferred location within the city.

Preparation required: Please bring 2-3 outfit options, arrive 10 minutes early, and let me know if you have any specific requirements or branding guidelines.

Perfect for executives, entrepreneurs, real estate agents, or anyone needing professional headshots for their business presence.`,
      image:
        "https://images.unsplash.com/photo-1520342898734-403d1d1a426a?q=80&w=2000",
      category: "Services > Photography > Event Services",
      created_at: "2024-04-20",
      updated_at: "2024-04-20",
      rating: 4.2,
      sponsored: true,
      type: "Service",
      tags: ["Sponsored"],
      price_per_hour: true,
      seller_info: {
        name: "TechTreasures",
        rating: 4.2,
        reviews: 98,
        member_since: "January 2024",
        credit_score: "87/100",
        location: "Toronto, ON",
        verified: true,
      },
      booking_options: {
        service_location: "I go to buyer's location (within 25km radius)",
        availability: "Monday - Friday",
        time_slots: "10:00 AM - 5:00 PM",
        payment: "Pay before service (Escrow protection)",
      },
    },
    {
      id: 3,
      name: "Luxury Watch Collection",
      seller: "Luxury Finds",
      price: "3500.00",
      status: "active",
      description: "Collection of 3 premium watches, mint condition",
      image:
        "https://images.unsplash.com/photo-1524805444758-089113d48a6d?q=80&w=2000",
      category: "Fashion",
      created_at: "2024-01-13",
      updated_at: "2024-01-13",
      rating: 4.2,
      sponsored: true,
    },
    {
      id: 4,
      name: "Premium Gaming Laptop Service",
      seller: "TechWiz",
      price: "1299.99",
      status: "active",
      description:
        "Expert cleaning and tune-up for high-performance gaming laptops.",
      image:
        "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=2000",
      category: "Services",
      created_at: "2024-04-20",
      updated_at: "2024-04-20",
      rating: 4.9,
      sponsored: true,
      type: "Service",
      tags: ["Sponsored", "New"],
      seller_info: {
        name: "TechWiz",
        rating: 4.9,
        reviews: 150,
        member_since: "March 2023",
        credit_score: "95/100",
        location: "Toronto, ON",
        verified: true,
      },
    },
  ];

  // Mock product images for the gallery
  const productImages = [
    "https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=800",
    "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?q=80&w=800",
    "https://images.unsplash.com/photo-1555940028-d872f2b4a536?q=80&w=800",
    "https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?q=80&w=800",
    "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?q=80&w=800",
  ];

  useEffect(() => {
    if (id) {
      fetchListing();
    }
  }, [id]);

  const fetchListing = async () => {
    setLoading(true);
    try {
      // For now, using mock data - replace with actual API call
      // const result = await sdk.callRestAPI(
      //   {
      //     where: { id: parseInt(id!) },
      //   },
      //   "GET",
      //   "listing"
      // );

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      const foundListing = mockListings.find((l) => l.id === parseInt(id!));
      setListing(foundListing || null);
    } catch (error) {
      console.error("Error fetching listing:", error);
      setListing(null);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!listing) return;

    setPurchasing(true);
    try {
      // Create a transaction/order
      const result = await sdk.callRestAPI(
        {
          listing_id: listing.id,
          seller_id: listing.seller_id,
          amount: parseFloat(listing.price),
          status: "pending",
        },
        "POST"
      );

      if (result.error) {
        console.error("Error creating transaction:", result.message);
        alert("Failed to initiate purchase. Please try again.");
      } else {
        alert(
          "Purchase initiated successfully! Check your transactions for updates."
        );
        navigate("/user/transactions");
      }
    } catch (error) {
      console.error("Error creating transaction:", error);
      alert("Failed to initiate purchase. Please try again.");
    } finally {
      setPurchasing(false);
    }
  };

  const handleContactSeller = () => {
    // This would typically open a chat or messaging interface
    alert("Contact seller functionality would be implemented here");
  };

  const handleMakeOffer = () => {
    setShowMakeOfferModal(true);
  };

  const handleSubmitOffer = () => {
    // Handle offer submission logic here
    console.log("Offer submitted:", {
      amount: offerAmount,
      message: offerMessage,
    });
    alert(`Offer of eb$${offerAmount} submitted successfully!`);
    setShowMakeOfferModal(false);
    setOfferAmount("");
    setOfferMessage("");
  };

  const handleCancelOffer = () => {
    setShowMakeOfferModal(false);
    setOfferAmount("");
    setOfferMessage("");
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) ? "text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  const getSimilarListings = () => {
    return mockListings.filter((l) => l.id !== listing?.id).slice(0, 3);
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  if (!listing) {
    return (
      <UserWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Listing not found</div>
            <InteractiveButton
              onClick={() => navigate("/user/marketplace")}
              className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
            >
              Back to Marketplace
            </InteractiveButton>
          </div>
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="bg-[#0D3166] min-h-screen text-white">
        <div className="container mx-auto px-6 py-8">
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-sm mb-6 hover:text-gray-300"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Listing Details
          </button>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-8">
              {/* Product Header */}
              <div>
                <h1 className="text-3xl font-bold mb-2">{listing.name}</h1>
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <span>Listed 3 days ago</span>
                  <span className="flex items-center gap-2 bg-gray-700 px-2 py-1 rounded">
                    <span>Type:</span>
                    <span className="font-semibold">{listing.type}</span>
                  </span>
                </div>
              </div>

              {/* Image Gallery */}
              <div>
                <div className="relative mb-4">
                  <img
                    src={productImages[selectedImageIndex]}
                    alt={listing.name}
                    className="w-full h-auto object-cover rounded-lg cursor-zoom-in"
                  />
                  <div className="absolute top-4 left-4">
                    {listing.sponsored && (
                      <span className="bg-yellow-400 text-black px-3 py-1 rounded text-xs font-bold flex items-center gap-1">
                        <StarIcon className="w-3 h-3" />
                        Sponsored
                      </span>
                    )}
                  </div>
                  <button className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2">
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                  <button className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full p-2">
                    <svg
                      className="w-6 h-6 text-gray-800"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
                    {selectedImageIndex + 1} / {productImages.length}
                  </div>
                  <div className="absolute bottom-4 right-4 text-sm flex items-center gap-1">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                    Click to zoom
                  </div>
                </div>
                <div className="flex gap-2">
                  {productImages.map((img, index) => (
                    <img
                      key={index}
                      src={img}
                      alt={`thumbnail ${index}`}
                      className={`w-20 h-20 object-cover rounded-md cursor-pointer border-2 ${
                        selectedImageIndex === index
                          ? "border-[#F52D2A]"
                          : "border-transparent"
                      }`}
                      onClick={() => setSelectedImageIndex(index)}
                    />
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h2 className="text-xl font-bold mb-4">Description</h2>
                <div
                  className="text-sm space-y-4 whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{
                    __html: listing.description
                      ? listing.description.replace(
                          /Key specifications:|This laptop is in excellent condition|This is perfect for gamers/g,
                          (match) => `<strong>${match}</strong>`
                        )
                      : "",
                  }}
                />
                <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center">
                  <div className="flex gap-4">
                    <span>
                      Listed on{" "}
                      {new Date(listing.created_at).toLocaleDateString()}
                    </span>
                    <span>
                      Expires on{" "}
                      {new Date(
                        new Date(listing.created_at).setMonth(
                          new Date(listing.created_at).getMonth() + 1
                        )
                      ).toLocaleDateString()}
                    </span>
                    <span>{listing.category?.split(">")[0].trim()}</span>
                  </div>
                  <button className="flex items-center gap-1 text-red-500 hover:underline">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 12a1 1 0 112 0 1 1 0 01-2 0zm1-8a1 1 0 011 1v4a1 1 0 11-2 0V5a1 1 0 011-1z" />
                    </svg>
                    Report Listing
                  </button>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200 flex items-center gap-4">
                  <span className="text-sm font-semibold">
                    Share this listing:
                  </span>
                  <div className="flex gap-3 text-xl">
                    <a href="#" className="hover:text-blue-600">
                      f
                    </a>
                    <a href="#" className="hover:text-blue-400">
                      in
                    </a>
                    <a href="#" className="hover:text-gray-600">
                      ✉
                    </a>
                  </div>
                </div>
              </div>

              {/* Delivery & Shipping or Booking Options */}
              {listing.type === "Service" && listing.booking_options ? (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">Booking Options</h2>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Service Location</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.service_location}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Availability</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.availability}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Time Slots</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.time_slots}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-blue-600 mt-1">●</span>
                      <div>
                        <h4 className="font-semibold">Payment</h4>
                        <p className="text-xs text-gray-500">
                          {listing.booking_options.payment}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white text-black p-6 rounded-lg">
                  <h2 className="text-xl font-bold mb-4">
                    Delivery & Shipping
                  </h2>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 1: eBa Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Trackable delivery managed by the platform.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 2: Other Local Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller delivers within the same city/region.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-red-500 mt-1">✗</span>
                      <div>
                        <h4 className="font-semibold">
                          Option 3: International Delivery
                        </h4>
                        <p className="text-xs text-gray-500">
                          Seller ships via FedEx, DHL, etc. to other countries.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 4: Pickup</h4>
                        <p className="text-xs text-gray-500">
                          Buyer collects the item from seller's location.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-green-500 mt-1">✓</span>
                      <div>
                        <h4 className="font-semibold">Option 5: Meet-up</h4>
                        <p className="text-xs text-gray-500">
                          Buyer and seller meet in a public place to exchange
                          the item.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Similar Listings */}
              <div>
                <h2 className="text-xl font-bold mb-4">Similar Listings</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getSimilarListings().map((item) => (
                    <div
                      key={item.id}
                      className="bg-white rounded-lg text-black overflow-hidden relative"
                    >
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-40 object-cover"
                      />
                      <div className="p-4">
                        <div className="flex gap-2 mb-2">
                          {item.sponsored && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                              ⭐ Sponsored
                            </span>
                          )}
                          {item.id === 2 && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              New
                            </span>
                          )}
                        </div>
                        <h4 className="font-semibold text-sm mb-2">
                          {item.name}
                        </h4>
                        <p className="text-[#F52D2A] font-bold text-lg mb-2">
                          eBa$ {item.price}
                        </p>
                        <p className="text-xs text-gray-500 mb-3 truncate">
                          {item.description}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            {renderStars(item.rating)}
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                            <span>{item.seller}</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
                          <span>{item.seller_info?.location}</span>
                          <span>Added {item.created_at}</span>
                        </div>
                      </div>
                      <button className="absolute top-2 right-2 bg-white rounded-full p-1.5 shadow">
                        <svg
                          className="w-4 h-4 text-gray-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column (Sticky) */}
            <div className="lg:sticky top-8 self-start space-y-6">
              {/* Purchase Card */}
              <div className="bg-white text-black p-6 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <p className="text-3xl font-bold text-[#0D3166]">
                    eBa$ {listing.price}
                    {listing.price_per_hour && "/hr"}
                  </p>
                  <button>
                    <svg
                      className="w-6 h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </button>
                </div>

                <p className="text-sm text-gray-500 mb-3 flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M17.707 3.293a1 1 0 00-1.414 0L15.586 4H12a1 1 0 00-1 1v.586l-2.707-2.707a1 1 0 00-1.414 0L5.293 4.464a1 1 0 000 1.414l7 7a1 1 0 001.414 0l4-4a1 1 0 000-1.414l-1.586-1.586zM11 5H6.414l1.293-1.293L11 6.414V5zM9 9a1 1 0 011-1h1a1 1 0 110 2H9a1 1 0 01-1-1zm-5 5.586l-1.293-1.293a1 1 0 010-1.414l1.586-1.586a1 1 0 011.414 0L8.414 11H13a1 1 0 011 1v.586l2.707 2.707a1 1 0 010 1.414l-1.586 1.586a1 1 0 01-1.414 0L12 16.586V18a1 1 0 01-1 1h-.586l-2.707 2.707a1 1 0 01-1.414 0L4.707 19.121a1 1 0 010-1.414l1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {listing.category}
                </p>

                <div className="flex gap-2 mb-4">
                  {listing.tags?.map((tag) => (
                    <span
                      key={tag}
                      className={`text-xs px-2 py-1 rounded font-semibold ${
                        tag === "Sponsored"
                          ? "bg-blue-100 text-[#0D3166]"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {tag === "Sponsored" && "⭐ "}
                      {tag}
                    </span>
                  ))}
                </div>

                {listing.type === "Service" ? (
                  <div className="space-y-3">
                    <InteractiveButton className="w-full bg-[#0D3166] text-white py-3 rounded-lg font-semibold">
                      Book Service
                    </InteractiveButton>
                    <p className="text-xs text-gray-500 text-center">
                      Payment will be held in escrow until service is confirmed.
                    </p>
                    <InteractiveButton className="w-full border border-gray-300 py-3 rounded-lg font-semibold text-gray-700 hover:bg-gray-50">
                      Make Offer
                    </InteractiveButton>
                  </div>
                ) : (
                  <>
                    {listing.category?.toLowerCase().includes("clothing") &&
                    listing.availableSizes ? (
                      <>
                        <div className="mb-4">
                          <label className="text-sm font-semibold text-gray-800 mb-2 block">
                            Select Size
                          </label>
                          <div className="flex gap-2 flex-wrap">
                            {listing.availableSizes.map((sizeInfo) => (
                              <button
                                key={sizeInfo.short}
                                onClick={() => setSelectedSize(sizeInfo.short)}
                                className={`flex flex-col items-center justify-center p-2 border rounded-md w-16 h-16 transition-colors ${
                                  selectedSize === sizeInfo.short
                                    ? "border-red-500 bg-red-50"
                                    : "border-gray-300"
                                }`}
                              >
                                <span className="font-bold text-gray-800">
                                  {sizeInfo.short}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {sizeInfo.long}
                                </span>
                              </button>
                            ))}
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mb-4 flex items-center gap-1">
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M14.08 7.36a.75.75 0 01.07 1.058l-3.32 4.98a.75.75 0 01-1.12.08l-2.43-2.43a.75.75 0 111.06-1.06l1.87 1.87 2.76-4.14a.75.75 0 011.06-.07zM18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-7a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75A.75.75 0 0110 3z" />
                          </svg>
                          Runs true to size. See chart for details.
                        </p>
                      </>
                    ) : (
                      <div className="flex items-center gap-4 mb-6">
                        <label className="text-sm font-medium">Quantity</label>
                        <div className="flex items-center border border-gray-300 rounded">
                          <button
                            onClick={() =>
                              setQuantity(Math.max(1, quantity - 1))
                            }
                            className="px-3 py-1 text-lg text-gray-500 hover:bg-gray-100"
                          >
                            -
                          </button>
                          <span className="px-4 py-1 text-center w-12">
                            {quantity}
                          </span>
                          <button
                            onClick={() => setQuantity(quantity + 1)}
                            className="px-3 py-1 text-lg text-gray-500 hover:bg-gray-100"
                          >
                            +
                          </button>
                        </div>
                      </div>
                    )}
                    <div className="space-y-3">
                      <InteractiveButton className="w-full bg-[#0D3166] text-white py-3 rounded-lg font-semibold">
                        Buy Now
                      </InteractiveButton>
                      <InteractiveButton className="w-full border border-gray-300 py-3 rounded-lg font-semibold text-gray-700 hover:bg-gray-50">
                        Make Offer
                      </InteractiveButton>
                    </div>
                    {listing.category?.toLowerCase().includes("clothing") && (
                      <button className="text-sm text-gray-700 mt-4 flex items-center justify-center w-full gap-1 hover:underline">
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M14.08 7.36a.75.75 0 01.07 1.058l-3.32 4.98a.75.75 0 01-1.12.08l-2.43-2.43a.75.75 0 111.06-1.06l1.87 1.87 2.76-4.14a.75.75 0 011.06-.07zM18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-7a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75A.75.75 0 0110 3z" />
                        </svg>
                        View Size Chart
                      </button>
                    )}
                  </>
                )}
              </div>

              {/* Seller Information */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h3 className="text-lg font-bold mb-4">Seller Information</h3>
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div>
                    <p className="font-bold">
                      {listing.seller_info?.name || "N/A"}
                    </p>
                    <div className="flex items-center gap-1 text-xs">
                      {renderStars(listing.seller_info?.rating)}
                      <span>
                        {listing.seller_info?.rating} (
                        {listing.seller_info?.reviews} ratings)
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-xs space-y-2">
                  <div className="flex justify-between">
                    <span>Member since</span>
                    <span className="font-semibold">
                      {listing.seller_info?.member_since}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Credit Score</span>
                    <span className="font-semibold">
                      {listing.seller_info?.credit_score}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Location</span>
                    <span className="font-semibold">
                      {listing.seller_info?.location}
                    </span>
                  </div>
                </div>
                {listing.seller_info?.verified && (
                  <p className="text-xs text-green-600 flex items-center gap-1 mt-4">
                    <svg
                      className="w-3 h-3"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Verified Seller
                  </p>
                )}
                <InteractiveButton className="w-full mt-4 border border-gray-300 py-2 rounded-lg font-semibold text-sm">
                  Contact Seller
                </InteractiveButton>
              </div>

              {/* Safety Tips */}
              <div className="bg-white text-black p-6 rounded-lg">
                <h3 className="text-lg font-bold mb-4">Safety Tips</h3>
                <ul className="text-xs space-y-2 text-gray-600">
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Always use
                    eBa's secure payment system
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Never share
                    personal financial information
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Review seller
                    ratings before purchasing
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span> Report
                    suspicious activity immediately
                  </li>
                </ul>
                <a
                  href="#"
                  className="text-xs text-blue-600 hover:underline mt-4 block text-center"
                >
                  Learn more about safe trading
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserMarketplaceDetailPage;
