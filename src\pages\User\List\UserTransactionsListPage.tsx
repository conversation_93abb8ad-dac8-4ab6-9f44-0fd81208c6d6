import React, { useState } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import { useNavigate } from "react-router-dom";
import { EyeIcon } from "@/assets/svgs/EyeIcon";
import TrashIcon from "@/assets/svgs/TrashIcon";
import DownloadIcon from "@/assets/svgs/DownloadIcon";
import SearchIcon from "@/assets/svgs/SearchIcon";

interface ITransaction {
  id: string;
  date: string;
  listing: string;
  type: "Sale" | "Purchase";
  counterparty: string;
  amount: string;
  fee: string;
  net_received_paid: string;
  status: "Completed" | "In Dispute" | "Refunded";
}

const mockTransactions: ITransaction[] = [
  {
    id: "1",
    date: "04/20",
    listing: "Wireless Headphones",
    type: "Sale",
    counterparty: "<PERSON>",
    amount: "eBa$ 129.9",
    fee: "eBa$ 6.5",
    net_received_paid: "eBa$ 123.49",
    status: "Completed",
  },
  {
    id: "2",
    date: "04/18",
    listing: "Digital Camera",
    type: "Purchase",
    counterparty: "Camera World",
    amount: "eBa$ 349.5",
    fee: "$3.49",
    net_received_paid: "eBa$ 349.50",
    status: "Completed",
  },
  {
    id: "3",
    date: "04/15",
    listing: "Graphic Design Services",
    type: "Sale",
    counterparty: "Sarah Johnson",
    amount: "eBa$ 129.9",
    fee: "eBa$ 6.5",
    net_received_paid: "eBa$ 123.49",
    status: "In Dispute",
  },
  {
    id: "4",
    date: "04/12",
    listing: "Vintage Lamp",
    type: "Purchase",
    counterparty: "Antique Treasures",
    amount: "eBa$ 349.5",
    fee: "$3.49",
    net_received_paid: "eBa$ 349.50",
    status: "Refunded",
  },
  {
    id: "5",
    date: "04/10",
    listing: "Smartphone Case",
    type: "Sale",
    counterparty: "David Lee",
    amount: "eBa$ 129.9",
    fee: "eBa$ 6.5",
    net_received_paid: "eBa$ 123.49",
    status: "Completed",
  },
  {
    id: "6",
    date: "04/08",
    listing: "Cooking Class",
    type: "Purchase",
    counterparty: "Chef Maria's Kitchen",
    amount: "eBa$ 349.5",
    fee: "$3.49",
    net_received_paid: "eBa$ 349.50",
    status: "Completed",
  },
];

interface IFilters {
  search: string;
  dateRange: string;
  transactionType: string;
  status: string;
}

const FilterComponent = ({
  filters,
  setFilters,
  applyFilters,
  activeTab,
  setActiveTab,
}: {
  filters: IFilters;
  setFilters: React.Dispatch<React.SetStateAction<IFilters>>;
  applyFilters: () => void;
  activeTab: string;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
}) => (
  <div className="bg-white rounded-lg p-4 mb-6">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div className="relative">
        <label
          htmlFor="search"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Search
        </label>
        <SearchIcon className="absolute top-9 left-3 w-5 h-5 text-gray-400" />
        <input
          type="text"
          id="search"
          placeholder="Search by item"
          value={filters.search}
          onChange={(e) => setFilters({ ...filters, search: e.target.value })}
          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        />
      </div>
      <div>
        <label
          htmlFor="date-range"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Date Range
        </label>
        <select
          id="date-range"
          value={filters.dateRange}
          onChange={(e) =>
            setFilters({ ...filters, dateRange: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>Last 30 days</option>
          <option>Last 7 days</option>
          <option>Last 90 days</option>
        </select>
      </div>
      <div>
        <label
          htmlFor="transaction-type"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Transaction Type
        </label>
        <select
          id="transaction-type"
          value={filters.transactionType}
          onChange={(e) =>
            setFilters({ ...filters, transactionType: e.target.value })
          }
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>All Transactions</option>
          <option>Sales</option>
          <option>Purchases</option>
        </select>
      </div>
      <div>
        <label
          htmlFor="status"
          className="block text-sm font-medium text-gray-500 mb-1"
        >
          Status
        </label>
        <select
          id="status"
          value={filters.status}
          onChange={(e) => setFilters({ ...filters, status: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-900"
        >
          <option>All Statuses</option>
          <option>Completed</option>
          <option>In Dispute</option>
          <option>Refunded</option>
        </select>
      </div>
    </div>
    <div className="mt-4 flex justify-between items-center">
      <div className="flex items-center space-x-2">
        {["All", "My Sales", "My Purchases"].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === tab
                ? "bg-[#0D3166] text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
      <button
        onClick={applyFilters}
        className="px-6 py-2 bg-[#0D3166] text-white rounded-md text-sm font-medium"
      >
        Apply Filters
      </button>
    </div>
  </div>
);

const UserTransactionsListPage = () => {
  const navigate = useNavigate();
  const [transactions] = useState<ITransaction[]>(mockTransactions);
  const [activeTab, setActiveTab] = useState("All");
  const [filters, setFilters] = useState<IFilters>({
    search: "",
    dateRange: "Last 30 days",
    transactionType: "All Transactions",
    status: "All Statuses",
  });
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getStatusPill = (status: ITransaction["status"]) => {
    const baseClasses = "px-3 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case "Completed":
        return `${baseClasses} bg-green-100 text-green-800`;
      case "In Dispute":
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case "Refunded":
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const applyFilters = () => {
    console.log("Applying filters:", filters);
    // Add filter logic here
  };

  const paginatedTransactions = transactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  const totalPages = Math.ceil(transactions.length / itemsPerPage);

  return (
    <UserWrapper>
      <div className="p-6 bg-[#0D3166] min-h-screen">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-white">Transaction History</h1>
          <button className="flex items-center px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-700">
            <DownloadIcon className="w-5 h-5 mr-2" />
            Export
          </button>
        </div>

        <FilterComponent
          filters={filters}
          setFilters={setFilters}
          applyFilters={applyFilters}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />

        <div className="bg-white rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  {[
                    "Date",
                    "Listing",
                    "Item",
                    "Service",
                    "Type",
                    "Counterparty",
                    "Amount",
                    "Fee",
                    "Net Received/Paid",
                    "Status",
                    "Actions",
                  ].map((header) => (
                    <th
                      key={header}
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                      {transaction.date}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 font-medium whitespace-nowrap">
                      {transaction.listing}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900"></td>
                    <td className="px-4 py-3 text-sm text-gray-900"></td>
                    <td className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                      {transaction.type}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900 whitespace-nowrap">
                      {transaction.counterparty}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                      {transaction.amount}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                      {transaction.fee}
                    </td>
                    <td className="px-4 py-3 text-sm font-bold text-gray-900 whitespace-nowrap">
                      {transaction.net_received_paid}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <span className={getStatusPill(transaction.status)}>
                        {transaction.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className="flex items-center space-x-2">
                        <button
                          className="text-gray-400 hover:text-gray-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(
                              `/user/transactions/view/${transaction.id}`
                            );
                          }}
                        >
                          <EyeIcon className="w-5 h-5" />
                        </button>
                        <button
                          className="text-gray-400 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle delete action
                          }}
                        >
                          <TrashIcon className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="flex items-center justify-between mt-6 mb-6">
          <div className="text-sm text-gray-300">
            Showing 1 to 6 of 24 entries
          </div>
          <nav className="flex items-center space-x-1">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 text-sm text-gray-300 hover:text-white disabled:opacity-50"
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-4 py-2 text-sm rounded-md ${
                  currentPage === page
                    ? "bg-[#F52D2A] text-white"
                    : "bg-[#0F2C59] text-white hover:bg-opacity-90"
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-sm text-gray-300 hover:text-white disabled:opacity-50"
            >
              Next
            </button>
          </nav>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Sales Summary</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              eBa$ 1,250.48
            </p>
            <p className="text-sm text-gray-500 flex items-center">
              <span className="text-green-500 mr-1">📈</span>
              12 transactions this month
            </p>
          </div>
          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Purchases Summary</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">eBa$ 845.25</p>
            <p className="text-sm text-gray-500 flex items-center">
              <span className="text-red-500 mr-1">📉</span>8 transactions this
              month
            </p>
          </div>
          <div className="bg-white rounded-lg p-6">
            <p className="text-sm text-gray-600 mb-1">Total Fees Paid</p>
            <p className="text-2xl font-bold text-gray-900 mb-1">eBa$ 89.75</p>
            <p className="text-sm text-gray-500 flex items-center">
              <span className="mr-1">ℹ️</span>
              Average fee: eBa$ 4.49
            </p>
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserTransactionsListPage;
